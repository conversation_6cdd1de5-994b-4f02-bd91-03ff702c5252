
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Portfolio at Risk (PAR) Report - 2025-04-03</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 20px;
                }
                
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                }
                
                h1, h2, h3 {
                    color: #2c3e50;
                }
                
                h1 {
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }
                
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                }
                
                .report-date {
                    font-size: 1.1em;
                    color: #7f8c8d;
                }
                
                .summary {
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 30px;
                    border-left: 4px solid #3498db;
                }
                
                .par-metric {
                    margin-bottom: 10px;
                }
                
                .par-label {
                    font-weight: bold;
                    width: 70px;
                    display: inline-block;
                }
                
                .par-value {
                    font-weight: bold;
                    font-size: 1.1em;
                    color: #e74c3c;
                }
                
                .par-details {
                    color: #7f8c8d;
                    margin-left: 10px;
                    font-size: 0.9em;
                }
                
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                    font-size: 0.9em;
                }
                
                th, td {
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }
                
                tr:hover {
                    background-color: #f5f5f5;
                }
                
                .total-row {
                    background-color: #eaf4fd;
                }
                
                .footer {
                    margin-top: 50px;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                    color: #7f8c8d;
                    font-size: 0.8em;
                }
                
                @media print {
                    body {
                        padding: 0;
                    }
                    
                    .container {
                        max-width: 100%;
                    }
                    
                    .action {
                        display: none; /* Hide export buttons when printing */
                    }
                }
                
                .action {
                    float: right;
                    margin-bottom: 20px;
                }
                
                span {
                    margin-right: 20px;
                }
                
                button {
                    padding: 6px 20px;
                    cursor: pointer;
                    margin-right: 5px;
                }
                
                #exportProgress {
                    display: none;
                    margin-top: 10px;
                    text-align: center;
                }
                
                .progress-container {
                    width: 100%;
                    background-color: #f1f1f1;
                    border-radius: 5px;
                    margin-top: 5px;
                }
                
                .progress-bar {
                    height: 20px;
                    background-color: #4CAF50;
                    border-radius: 5px;
                    width: 0%;
                    transition: width 0.3s;
                }
            </style>
            <!-- Include SheetJS library for Excel exports -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
            <!-- Include html2pdf library for PDF export -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
            <script>
                // Function to show progress during export
                function showExportProgress() {
                    document.getElementById('exportProgress').style.display = 'block';
                    document.getElementById('exportButtons').style.display = 'none';
                    
                    // Simulate progress for better UX
                    let progress = 0;
                    const progressBar = document.getElementById('progressBar');
                    const progressText = document.getElementById('progressText');
                    
                    const interval = setInterval(() => {
                        if (progress >= 100) {
                            clearInterval(interval);
                            // Hide progress after export completes
                            setTimeout(() => {
                                document.getElementById('exportProgress').style.display = 'none';
                                document.getElementById('exportButtons').style.display = 'block';
                                progressText.textContent = 'Export complete!';
                            }, 500);
                        } else {
                            progress += 5;
                            progressBar.style.width = progress + '%';
                            progressText.textContent = 'Exporting data: ' + progress + '%';
                        }
                    }, 100);
                }
                
                // Function to export table data to Excel formats
                function exportData(type) {
                    showExportProgress();
                    
                    setTimeout(() => {
                        try {
                            const fileName = 'PAR_Report.' + type;
                            const table = document.getElementById("resulta");
                            
                            // Use timeout to ensure UI updates before heavy processing
                            const wb = XLSX.utils.table_to_book(table);
                            XLSX.writeFile(wb, fileName);
                            
                            console.log('Export completed successfully');
                        } catch (error) {
                            console.error('Export failed:', error);
                            alert('Export failed: ' + error.message);
                            
                            // Reset UI in case of error
                            document.getElementById('exportProgress').style.display = 'none';
                            document.getElementById('exportButtons').style.display = 'block';
                        }
                    }, 500);
                }
                
                // Function to export the entire report as PDF
                function exportToPDF() {
                    showExportProgress();
                    
                    setTimeout(() => {
                        try {
                            // Hide export buttons during PDF generation (they'll be restored by showExportProgress)
                            const content = document.querySelector('.container');
                            
                            // Configure PDF options
                            const options = {
                                margin: 10,
                                filename: 'PAR_Report.pdf',
                                image: { type: 'jpeg', quality: 0.98 },
                                html2canvas: { scale: 2 },
                                jsPDF: { unit: 'mm', format: 'a4', orientation: 'landscape' }
                            };
                            
                            // Generate the PDF
                            html2pdf().from(content).set(options).save();
                            
                            console.log('PDF export completed successfully');
                        } catch (error) {
                            console.error('PDF export failed:', error);
                            alert('PDF export failed: ' + error.message);
                            
                            // Reset UI in case of error
                            document.getElementById('exportProgress').style.display = 'none';
                            document.getElementById('exportButtons').style.display = 'block';
                        }
                    }, 500);
                }
            </script>
        </head>
        <body>
            <div class="container">
                <div class="report-header">
                    <h1>Portfolio at Risk (PAR) Report Sycamore Credit</h1>
                    <div class="report-date">Generated on: 2025-04-03</div>
                </div>
                
                <div class="action">
                    <div id="exportButtons">
                        <span>Export as:</span>
                        <button onclick="exportData('xlsx')">Excel (xlsx)</button>
                        <button onclick="exportData('xls')">Excel (xls)</button>
                        <button onclick="exportData('csv')">CSV</button>
                        <button onclick="exportToPDF()" style="background-color:#e74c3c; color:white;">PDF</button>
                    </div>
                    <div id="exportProgress">
                        <div id="progressText">Preparing export...</div>
                        <div class="progress-container">
                            <div id="progressBar" class="progress-bar"></div>
                        </div>
                    </div>
                </div>
                
                <div class="summary">
                    <h2>Summary</h2>
                    
        <div class="par-metric">
            <span class="par-label">PAR1:</span>
            <span class="par-value">11.7%</span>
            <span class="par-details">
                (K7,999,999.99 at risk out of K68,600,000.00)
            </span>
        </div>
    
        <div class="par-metric">
            <span class="par-label">PAR30:</span>
            <span class="par-value">11.7%</span>
            <span class="par-details">
                (K7,999,999.99 at risk out of K68,600,000.00)
            </span>
        </div>
    
        <div class="par-metric">
            <span class="par-label">PAR60:</span>
            <span class="par-value">11.7%</span>
            <span class="par-details">
                (K7,999,999.99 at risk out of K68,600,000.00)
            </span>
        </div>
    
        <div class="par-metric">
            <span class="par-label">PAR90:</span>
            <span class="par-value">7.3%</span>
            <span class="par-details">
                (K5,000,000.00 at risk out of K68,600,000.00)
            </span>
        </div>
    
                </div>
                
                <h2>Loan Details in Arrears</h2>
                <table id="resulta">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Loan ID</th>
                            <th>Customer</th>
                            <th>Officer</th>
                            <th># Payments in Arrears</th>
                            <th>Oldest Arrears (Days)</th>
                            <th>Missed Payment Details</th>
                            <th>Paid Schedules</th>
                            <th>Remaining Schedules</th>
                            <th>Paid Amount</th>
                            <th>Outstanding Principal</th>
                            <th>Total Outstanding</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        
        <tr>
            <td>Masamba</td>
            <td>SCL20250401104</td>
            <td>Jameson Kaluluma</td>
            <td>Mphatso Kachere</td>
            <td>4</td>
            <td>99</td>
            <td>Payment #1 (2024-12-25) - 99 days late<br>Payment #2 (2025-01-25) - 68 days late<br>Payment #3 (2025-02-25) - 37 days late<br>Payment #4 (2025-03-25) - 9 days late</td>
            <td>0/6</td>
            <td>6/6</td>
            <td>K0.00</td>
            <td>K5,000,000.00</td>
            <td>K7,400,393.46</td>
            <td>NOT PAID</td>
        </tr>
    
        <tr>
            <td>Masamba</td>
            <td>SCL20250401109</td>
            <td>Julius Chimbalangondo</td>
            <td>ALEX KAMPUTA</td>
            <td>3</td>
            <td>89</td>
            <td>Payment #1 (2025-01-04) - 89 days late<br>Payment #2 (2025-02-04) - 58 days late<br>Payment #3 (2025-03-04) - 30 days late</td>
            <td>0/6</td>
            <td>6/6</td>
            <td>K0.00</td>
            <td>K2,999,999.99</td>
            <td>K4,440,236.04</td>
            <td>NOT PAID</td>
        </tr>
    
                    </tbody>
                </table>
                
                <h2>PAR Age Analysis</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Age Group</th>
                            <th>Number of Loans</th>
                            <th>Outstanding Principal</th>
                            <th>% of Portfolio</th>
                        </tr>
                    </thead>
                    <tbody>
                        
        <tr>
            <td>PAR 1-30</td>
            <td>0</td>
            <td>K0.00</td>
            <td>0.0%</td>
        </tr>
    
        <tr>
            <td>PAR 31-60</td>
            <td>0</td>
            <td>K0.00</td>
            <td>0.0%</td>
        </tr>
    
        <tr>
            <td>PAR 61-90</td>
            <td>1</td>
            <td>K2,999,999.99</td>
            <td>4.4%</td>
        </tr>
    
        <tr>
            <td>PAR > 90</td>
            <td>1</td>
            <td>K5,000,000.00</td>
            <td>7.3%</td>
        </tr>
    
                        
        <tr class="total-row">
            <td><strong>Total PAR</strong></td>
            <td><strong>2</strong></td>
            <td><strong>K7,999,999.99</strong></td>
            <td><strong>11.7%</strong></td>
        </tr>
    
                    </tbody>
                </table>
                
                <div class="footer">
                    <p>This report shows the entire remaining balance of loans with at least one payment in arrears beyond the specified threshold.</p>
                    <p>© 2025 Your Financial Institution. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
    