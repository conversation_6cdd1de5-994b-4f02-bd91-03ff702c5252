<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>*|MC:SUBJECT|*</title>
	<style type="text/css">
		/* Client-specific Styles */
		#outlook a{padding:0;} /* Force Outlook to provide a "view in browser" button. */
		body{width:100% !important;} .ReadMsgBody{width:100%;} .ExternalClass{width:100%;} /* Force Hotmail to display emails at full width */
		body{-webkit-text-size-adjust:none;} /* Prevent Webkit platforms from changing default text sizes. */

		/* Reset Styles */
		body{margin:0; padding:0;}
		img{border:0; height:auto; line-height:100%; outline:none; text-decoration:none;}
		table td{border-collapse:collapse;}
		#backgroundTable{height:100% !important; margin:0; padding:0; width:100% !important;}

		/* Template Styles */

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: COMMON PAGE ELEMENTS /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Page
		* @section background color
		* @tip Set the background color for your email. You may want to choose one that matches your company's branding.
		* @theme page
		*/
		body, #backgroundTable{
			/*@editable*/ background-color:#FAFAFA;
		}

		/**
		* @tab Page
		* @section email border
		* @tip Set the border for your email.
		*/
		#templateContainer{
			/*@editable*/ border:0;
		}

		/**
		* @tab Page
		* @section heading 1
		* @tip Set the styling for all first-level headings in your emails. These should be the largest of your headings.
		* @style heading 1
		*/
		h1, .h1{
			/*@editable*/ color:#202020;
			display:block;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:40px;
			/*@editable*/ font-weight:bold;
			/*@editable*/ line-height:100%;
			margin-top:2%;
			margin-right:0;
			margin-bottom:1%;
			margin-left:0;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Page
		* @section heading 2
		* @tip Set the styling for all second-level headings in your emails.
		* @style heading 2
		*/
		h2, .h2{
			/*@editable*/ color:#404040;
			display:block;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:18px;
			/*@editable*/ font-weight:bold;
			/*@editable*/ line-height:100%;
			margin-top:2%;
			margin-right:0;
			margin-bottom:1%;
			margin-left:0;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Page
		* @section heading 3
		* @tip Set the styling for all third-level headings in your emails.
		* @style heading 3
		*/
		h3, .h3{
			/*@editable*/ color:#606060;
			display:block;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:16px;
			/*@editable*/ font-weight:bold;
			/*@editable*/ line-height:100%;
			margin-top:2%;
			margin-right:0;
			margin-bottom:1%;
			margin-left:0;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Page
		* @section heading 4
		* @tip Set the styling for all fourth-level headings in your emails. These should be the smallest of your headings.
		* @style heading 4
		*/
		h4, .h4{
			/*@editable*/ color:#808080;
			display:block;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:14px;
			/*@editable*/ font-weight:bold;
			/*@editable*/ line-height:100%;
			margin-top:2%;
			margin-right:0;
			margin-bottom:1%;
			margin-left:0;
			/*@editable*/ text-align:left;
		}

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: PREHEADER /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Header
		* @section preheader style
		* @tip Set the background color for your email's preheader area.
		* @theme page
		*/
		#templatePreheader{
			/*@editable*/ background-color:#FAFAFA;
		}

		/**
		* @tab Header
		* @section preheader text
		* @tip Set the styling for your email's preheader text. Choose a size and color that is easy to read.
		*/
		.preheaderContent div{
			/*@editable*/ color:#707070;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:10px;
			/*@editable*/ line-height:100%;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Header
		* @section preheader link
		* @tip Set the styling for your email's preheader links. Choose a color that helps them stand out from your text.
		*/
		.preheaderContent div a:link, .preheaderContent div a:visited, /* Yahoo! Mail Override */ .preheaderContent div a .yshortcuts /* Yahoo! Mail Override */{
			/*@editable*/ color:#336699;
			/*@editable*/ font-weight:normal;
			/*@editable*/ text-decoration:underline;
		}

		/**
		* @tab Header
		* @section social bar style
		* @tip Set the background color and border for your email's footer social bar.
		*/
		#social div{
			/*@editable*/ text-align:right;
		}

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: HEADER /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Header
		* @section header style
		* @tip Set the background color and border for your email's header area.
		* @theme header
		*/
		#templateHeader{
			/*@editable*/ background-color:#FFFFFF;
			/*@editable*/ border-bottom:5px solid #505050;
		}

		/**
		* @tab Header
		* @section header text
		* @tip Set the styling for your email's header text. Choose a size and color that is easy to read.
		*/
		.headerContent{
			/*@editable*/ color:#202020;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:34px;
			/*@editable*/ font-weight:bold;
			/*@editable*/ line-height:100%;
			/*@editable*/ padding:10px;
			/*@editable*/ text-align:right;
			/*@editable*/ vertical-align:middle;
		}

		/**
		* @tab Header
		* @section header link
		* @tip Set the styling for your email's header links. Choose a color that helps them stand out from your text.
		*/
		.headerContent a:link, .headerContent a:visited, /* Yahoo! Mail Override */ .headerContent a .yshortcuts /* Yahoo! Mail Override */{
			/*@editable*/ color:#336699;
			/*@editable*/ font-weight:normal;
			/*@editable*/ text-decoration:underline;
		}

		#headerImage{
			height:auto;
			max-width:600px !important;
		}

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: MAIN BODY /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Body
		* @section body style
		* @tip Set the background color for your email's body area.
		*/
		#templateContainer, .bodyContent{
			/*@editable*/ background-color:#FDFDFD;
		}

		/**
		* @tab Body
		* @section body text
		* @tip Set the styling for your email's main content text. Choose a size and color that is easy to read.
		* @theme main
		*/
		.bodyContent div{
			/*@editable*/ color:#505050;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:14px;
			/*@editable*/ line-height:150%;
			/*@editable*/ text-align:justify;
		}

		/**
		* @tab Body
		* @section body link
		* @tip Set the styling for your email's main content links. Choose a color that helps them stand out from your text.
		*/
		.bodyContent div a:link, .bodyContent div a:visited, /* Yahoo! Mail Override */ .bodyContent div a .yshortcuts /* Yahoo! Mail Override */{
			/*@editable*/ color:#336699;
			/*@editable*/ font-weight:normal;
			/*@editable*/ text-decoration:underline;
		}

		.bodyContent img{
			display:inline;
			height:auto;
		}

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: SIDEBAR /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Sidebar
		* @section sidebar style
		* @tip Set the background color and border for your email's sidebar area.
		*/
		#templateSidebar{
			/*@editable*/ background-color:#FDFDFD;
		}

		/**
		* @tab Sidebar
		* @section sidebar style
		* @tip Set the background color and border for your email's sidebar area.
		*/
		.sidebarContent{
			/*@editable*/ border-right:1px solid #DDDDDD;
		}

		/**
		* @tab Sidebar
		* @section sidebar text
		* @tip Set the styling for your email's sidebar text. Choose a size and color that is easy to read.
		*/
		.sidebarContent div{
			/*@editable*/ color:#505050;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:10px;
			/*@editable*/ line-height:150%;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Sidebar
		* @section sidebar link
		* @tip Set the styling for your email's sidebar links. Choose a color that helps them stand out from your text.
		*/
		.sidebarContent div a:link, .sidebarContent div a:visited, /* Yahoo! Mail Override */ .sidebarContent div a .yshortcuts /* Yahoo! Mail Override */{
			/*@editable*/ color:#336699;
			/*@editable*/ font-weight:normal;
			/*@editable*/ text-decoration:underline;
		}

		.sidebarContent img{
			display:inline;
			height:auto;
		}

		/* /\/\/\/\/\/\/\/\/\/\ STANDARD STYLING: FOOTER /\/\/\/\/\/\/\/\/\/\ */

		/**
		* @tab Footer
		* @section footer style
		* @tip Set the background color and top border for your email's footer area.
		* @theme footer
		*/
		#templateFooter{
			/*@editable*/ background-color:#FAFAFA;
			/*@editable*/ border-top:3px solid #909090;
		}

		/**
		* @tab Footer
		* @section footer text
		* @tip Set the styling for your email's footer text. Choose a size and color that is easy to read.
		* @theme footer
		*/
		.footerContent div{
			/*@editable*/ color:#707070;
			/*@editable*/ font-family:Arial;
			/*@editable*/ font-size:11px;
			/*@editable*/ line-height:125%;
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Footer
		* @section footer link
		* @tip Set the styling for your email's footer links. Choose a color that helps them stand out from your text.
		*/
		.footerContent div a:link, .footerContent div a:visited, /* Yahoo! Mail Override */ .footerContent div a .yshortcuts /* Yahoo! Mail Override */{
			/*@editable*/ color:#336699;
			/*@editable*/ font-weight:normal;
			/*@editable*/ text-decoration:underline;
		}

		.footerContent img{
			display:inline;
		}

		/**
		* @tab Footer
		* @section social bar style
		* @tip Set the background color and border for your email's footer social bar.
		* @theme footer
		*/
		#social{
			/*@editable*/ background-color:#FFFFFF;
			/*@editable*/ border:0;
		}

		/**
		* @tab Footer
		* @section social bar style
		* @tip Set the background color and border for your email's footer social bar.
		*/
		#social div{
			/*@editable*/ text-align:left;
		}

		/**
		* @tab Footer
		* @section utility bar style
		* @tip Set the background color and border for your email's footer utility bar.
		* @theme footer
		*/
		#utility{
			/*@editable*/ background-color:#FAFAFA;
			/*@editable*/ border-top:0;
		}

		/**
		* @tab Footer
		* @section utility bar style
		* @tip Set the background color and border for your email's footer utility bar.
		*/
		#utility div{
			/*@editable*/ text-align:left;
		}

		#monkeyRewards img{
			max-width:170px !important;
		}
	</style>
</head>
<body leftmargin="0" marginwidth="0" topmargin="0" marginheight="0" offset="0">
<center>
	<table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="backgroundTable">
		<tr>
			<td align="center" valign="top">
				<!-- // Begin Template Preheader \\ -->
				<table border="0" cellpadding="10" cellspacing="0" width="600" id="templatePreheader">
					<tr>
						<td valign="top" class="preheaderContent">

							<!-- // Begin Module: Standard Preheader \ -->
							<table border="0" cellpadding="10" cellspacing="0" width="100%">
								<tr>
									<td valign="top">
										<div mc:edit="std_preheader_content">
											Use this area to offer a short teaser of your email's content. Text here will show in the preview area of some email clients.
										</div>
									</td>
									<!-- *|IFNOT:ARCHIVE_PAGE|* -->
									<td valign="top" width="170">
										<div mc:edit="std_preheader_links">
											Email not displaying correctly?<br /><a href="*|ARCHIVE|*" target="_blank">View it in your browser</a>.
										</div>
									</td>
									<!-- *|END:IF|* -->
								</tr>
							</table>
							<!-- // End Module: Standard Preheader \ -->

						</td>
					</tr>
				</table>
				<!-- // End Template Preheader \\ -->
				<table border="0" cellpadding="0" cellspacing="0" width="600" id="templateContainer">
					<tr>
						<td align="center" valign="top">
							<!-- // Begin Template Header \\ -->
							<table border="0" cellpadding="0" cellspacing="0" width="600" id="templateHeader">
								<tr>
									<td class="headerContent">
										<img src="http://gallery.mailchimp.com/27aac8a65e64c994c4416d6b8/images/placeholder_180x100.gif" style="max-width:180px;" id="headerImage campaign-icon" mc:label="header_image" mc:edit="header_image" mc:allowtext />
									</td>
									<td class="headerContent" width="100%" style="padding-left:10px; padding-right:20px;">
										<div mc:edit="Header_content">
											<h1>Heading 1</h1>
										</div>
									</td>
								</tr>
							</table>
							<!-- // End Template Header \\ -->
						</td>
					</tr>
					<tr>
						<td align="center" valign="top">
							<!-- // Begin Template Body \\ -->
							<table border="0" cellpadding="10" cellspacing="0" width="600" id="templateBody">
								<tr>
									<!-- // Begin Sidebar \\  -->
									<td valign="top" width="180" id="templateSidebar">
										<table border="0" cellpadding="0" cellspacing="0" width="100%">
											<tr>
												<td valign="top">

													<!-- // Begin Module: Standard Content \\ -->
													<table border="0" cellpadding="20" cellspacing="0" width="100%" class="sidebarContent">
														<tr>
															<td valign="top" style="padding-left:10px;">
																<div mc:edit="std_content01">
																	<strong>Basic content module</strong>
																	<br />
																	Far far away, behind the word mountains.
																	<br />
																	<br />
																	<strong>Far from the countries</strong>
																	<br />
																	Vokalia and Consonantia, there live the blind texts.
																</div>
															</td>
														</tr>
													</table>
													<!-- // End Module: Standard Content \\ -->

												</td>
											</tr>
										</table>
									</td>
									<!-- // End Sidebar \\ -->
									<td valign="top" class="bodyContent">

										<!-- // Begin Module: Standard Content \\ -->
										<table border="0" cellpadding="10" cellspacing="0" width="100%">
											<tr>
												<td valign="top" style="padding-left:0;">
													<div mc:edit="std_content00">
														<h2 class="h2">Heading 2</h2>
														<h3 class="h3">Heading 3</h3>
														<strong>Getting started:</strong> Customize your template by clicking on the style editor tabs up above. Set your fonts, colors, and styles. After setting your styling is all done you can click here in this area, delete the text, and start adding your own awesome content!
														<br />
														<br />
														<h4 class="h4">Heading 4</h4>
														After you enter your content, highlight the text you want to style and select the options you set in the style editor in the "styles" drop down box. Want to <a href="http://www.mailchimp.com/kb/article/im-using-the-style-designer-and-i-cant-get-my-formatting-to-change" target="_blank">get rid of styling on a bit of text</a>, but having trouble doing it? Just use the "remove formatting" button to strip the text of any formatting and reset your style.
													</div>
												</td>
											</tr>
										</table>
										<!-- // End Module: Standard Content \\ -->

									</td>
								</tr>
							</table>
							<!-- // End Template Body \\ -->
						</td>
					</tr>
					<tr>
						<td align="center" valign="top">
							<!-- // Begin Template Footer \\ -->
							<table border="0" cellpadding="0" cellspacing="0" width="600" id="templateFooter">
								<tr>
									<td valign="top" class="footerContent">

										<!-- // Begin Module: Standard Footer \\ -->
										<table border="0" cellpadding="10" cellspacing="0" width="100%">
											<tr>
												<td colspan="2" valign="middle" id="social">
													<div mc:edit="std_social">
														&nbsp;<a href="*|TWITTER:PROFILEURL|*">follow on Twitter</a> | <a href="*|FACEBOOK:PROFILEURL|*">friend on Facebook</a> | <a href="*|FORWARD|*">forward to a friend</a>&nbsp;
													</div>
												</td>
											</tr>
											<tr>
												<td valign="top" width="350">
													<div mc:edit="std_footer">
														<em>Copyright &copy; *|CURRENT_YEAR|* *|LIST:COMPANY|*, All rights reserved.</em>
														<br />
														*|IFNOT:ARCHIVE_PAGE|* *|LIST:DESCRIPTION|*
														<br />
														<strong>Our mailing address is:</strong>
														<br />
														*|HTML:LIST_ADDRESS_HTML|**|END:IF|*
													</div>
												</td>
												<td valign="top" width="190" id="monkeyRewards">
													<div mc:edit="monkeyrewards">
														*|IF:REWARDS|* *|HTML:REWARDS|* *|END:IF|*
													</div>
												</td>
											</tr>
											<tr>
												<td colspan="2" valign="middle" id="utility">
													<div mc:edit="std_utility">
														&nbsp;<a href="*|UNSUB|*">unsubscribe from this list</a> | <a href="*|UPDATE_PROFILE|*">update subscription preferences</a>&nbsp;
													</div>
												</td>
											</tr>
										</table>
										<!-- // End Module: Standard Footer \\ -->

									</td>
								</tr>
							</table>
							<!-- // End Template Footer \\ -->
						</td>
					</tr>
				</table>
				<br />
			</td>
		</tr>
	</table>
</center>
</body>
</html>
