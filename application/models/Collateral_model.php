<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Collateral_model extends CI_Model
{

    public $table = 'collateral';
    public $id = 'collateral_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }
    function get_by_loan_id($id)
    {
        $this->db->where('loan_id',$id);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('collateral_id', $q);
	$this->db->or_like('loan_id', $q);
	$this->db->or_like('collateral_name', $q);
	$this->db->or_like('collateral_type', $q);
	$this->db->or_like('serial', $q);
	$this->db->or_like('estimated_price', $q);
	$this->db->or_like('attachement', $q);
	$this->db->or_like('description', $q);
	$this->db->or_like('date_added', $q);
	$this->db->or_like('added_by', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('collateral_id', $q);
	$this->db->or_like('loan_id', $q);
	$this->db->or_like('collateral_name', $q);
	$this->db->or_like('collateral_type', $q);
	$this->db->or_like('serial', $q);
	$this->db->or_like('estimated_price', $q);
	$this->db->or_like('attachement', $q);
	$this->db->or_like('description', $q);
	$this->db->or_like('date_added', $q);
	$this->db->or_like('added_by', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Collateral_model.php */
/* Location: ./application/models/Collateral_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2022-11-22 16:53:03 */
/* http://harviacode.com */